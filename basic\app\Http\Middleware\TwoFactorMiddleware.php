<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TwoFactorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Si l'utilisateur n'est pas connecté, laisser passer
        if (!$user) {
            return $next($request);
        }

        // Si la 2FA n'est pas activée pour cet utilisateur, laisser passer
        if (!$user->two_factor_enabled) {
            return $next($request);
        }

        // Si l'utilisateur est déjà vérifié pour cette session, laisser passer
        if (session('2fa_verified')) {
            return $next($request);
        }

        // Si l'utilisateur est sur une route 2FA, laisser passer
        if ($request->routeIs('2fa.*') || $request->routeIs('logout')) {
            return $next($request);
        }

        // Générer un code 2FA et rediriger vers la page de vérification
        $user->generateTwoFactorCode();
        
        return redirect()->route('2fa.verify.show')
            ->with('status', 'Un code de vérification a été envoyé à votre adresse email.');
    }
}
