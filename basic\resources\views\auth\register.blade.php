<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Register | Inventory Management System</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Inventory Management System Admin Dashboard" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('backend/assets/images/favicon.ico') }}">

    <!-- App css -->
    <link href="{{ asset('backend/assets/css/app.min.css') }}" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons -->
    <link href="{{ asset('backend/assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom CSS -->
    <style>
        .error-message {
            color: #dc3545;
            font-weight: 600;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>

<body class="bg-white">
    <!-- Begin page -->
    <div class="account-page">
        <div class="container-fluid p-0">
            <div class="row align-items-center g-0">
                <div class="col-xl-5">
                    <div class="row">
                        <div class="col-md-7 mx-auto">
                            <div class="mb-0 border-0 p-md-5 p-lg-0 p-4">
                                <div class="mb-4 p-0">
                                    <a href="/" class="auth-logo">
                                        <img src="{{ asset('backend/assets/images/logo-dark.png') }}" alt="logo-dark"
                                            class="mx-auto" height="28" />
                                    </a>
                                </div>

                                <div class="pt-0">
                                    <form method="POST" action="{{ route('register') }}" class="my-4">
                                        @csrf
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">{{ __('Full Name') }}</label>
                                            <input class="form-control" type="text" id="name" name="name"
                                                required autofocus placeholder="{{ __('Enter your full name') }}"
                                                value="{{ old('name') }}" autocomplete="name">
                                            <x-input-error :messages="$errors->get('name')" class="error-message" />
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">{{ __('Email') }}</label>
                                            <input class="form-control" type="email" id="email" name="email"
                                                required placeholder="{{ __('Enter your email') }}"
                                                value="{{ old('email') }}" autocomplete="username">
                                            <x-input-error :messages="$errors->get('email')" class="error-message" />
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="password" class="form-label">{{ __('Password') }}</label>
                                            <input class="form-control" type="password" id="password" name="password"
                                                required placeholder="{{ __('Enter your password') }}"
                                                autocomplete="new-password">
                                            <x-input-error :messages="$errors->get('password')" class="error-message" />
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="password_confirmation"
                                                class="form-label">{{ __('Confirm Password') }}</label>
                                            <input class="form-control" type="password" id="password_confirmation"
                                                name="password_confirmation" required
                                                placeholder="{{ __('Confirm your password') }}"
                                                autocomplete="new-password">
                                            <x-input-error :messages="$errors->get('password_confirmation')" class="error-message" />
                                        </div>

                                        <div class="form-group mb-0 row">
                                            <div class="col-12">
                                                <div class="d-grid">
                                                    <button class="btn btn-primary"
                                                        type="submit">{{ __('Register') }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>

                                    <div class="text-center text-muted mb-4">
                                        <p class="mb-0">{{ __('Already have an account?') }} <a
                                                class='text-primary ms-2 fw-medium'
                                                href="{{ route('login') }}">{{ __('Login here') }}</a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-7">
                    <div class="account-page-bg p-md-5 p-4">
                        <div class="text-center">
                            <h3 class="text-dark mb-3 pera-title">Inventory Management System</h3>
                            <div class="auth-image">
                                <img src="{{ asset('backend/assets/images/authentication.svg') }}"
                                    class="mx-auto img-fluid" alt="Registration illustration">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END wrapper -->

    <!-- Vendor -->
    <script src="{{ asset('backend/assets/libs/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/waypoints/lib/jquery.waypoints.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/jquery.counterup/jquery.counterup.min.js') }}"></script>
    <script src="{{ asset('backend/assets/libs/feather-icons/feather.min.js') }}"></script>

    <!-- App js-->
    <script src="{{ asset('backend/assets/js/app.js') }}"></script>
</body>

</html>
