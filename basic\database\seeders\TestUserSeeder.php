<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer un utilisateur de test avec 2FA activée
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'two_factor_enabled' => true,
                'role' => 'admin',
                'status' => '1',
                'email_verified_at' => now(),
            ]
        );

        // Créer un utilisateur de test sans 2FA
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'two_factor_enabled' => false,
                'role' => 'user',
                'status' => '1',
                'email_verified_at' => now(),
            ]
        );
    }
}
