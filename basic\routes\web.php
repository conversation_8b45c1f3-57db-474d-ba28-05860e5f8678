<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ProfileController;


Route::get('/', function () {
    return view('welcome');
});

// Routes 2FA
Route::middleware(['auth'])->group(function () {
    Route::controller(App\Http\Controllers\TwoFactorController::class)->group(function () {
        Route::get('/2fa', 'index')->name('2fa.index');
        Route::post('/2fa/toggle', 'toggle')->name('2fa.toggle');
        Route::get('/2fa/verify', 'show')->name('2fa.verify.show');
        Route::post('/2fa/verify', 'verify')->name('2fa.verify');
        Route::get('/2fa/resend', 'resend')->name('2fa.resend');
        Route::get('/2fa/generate', 'generateCode')->name('2fa.generate');
    });
});

Route::get('/dashboard', function () {
    return view('admin.index');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    //logout
    Route::get('/admin/logout', [AdminController::class, 'AdminLogout'])->name('admin.logout');

});

require __DIR__ . '/auth.php';

// Using the AdminController
