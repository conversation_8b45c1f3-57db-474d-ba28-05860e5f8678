
<?php $__env->startSection('admin'); ?>
    <div class="content">
        <div class="container-xxl">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Authentification à deux facteurs</h4>
                        </div>
                        <div class="card-body">
                            <?php if(session('status')): ?>
                                <div class="alert alert-success">
                                    <?php echo e(session('status')); ?>

                                </div>
                            <?php endif; ?>

                            <form action="<?php echo e(route('2fa.toggle')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="form-group mb-3">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="twoFactorEnabled"
                                            name="two_factor_enabled"
                                            <?php echo e(auth()->user()->two_factor_enabled ? 'checked' : ''); ?>

                                            onchange="this.form.submit()">
                                        <label class="custom-control-label" for="twoFactorEnabled">
                                            <?php echo e(auth()->user()->two_factor_enabled ? 'Désactiver' : 'Activer'); ?>

                                            l'authentification à deux facteurs
                                        </label>
                                    </div>
                                </div>
                            </form>

                            <?php if(auth()->user()->two_factor_enabled): ?>
                                <div class="mt-4">
                                    <h5>Configuration</h5>
                                    <p>
                                        L'authentification à deux facteurs est actuellement <strong>activée</strong>.<br>
                                        À chaque connexion, vous recevrez un code de vérification par email.
                                    </p>
                                </div>
                            <?php else: ?>
                                <div class="mt-4">
                                    <h5>Pourquoi activer l'authentification à deux facteurs ?</h5>
                                    <p>
                                        L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à
                                        votre compte.
                                        En plus de votre mot de passe, vous devrez saisir un code unique envoyé à votre
                                        adresse email.
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\inventory25\basic\resources\views/auth/two-factor.blade.php ENDPATH**/ ?>