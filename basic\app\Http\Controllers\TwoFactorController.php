<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Mail\VerificationCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

class TwoFactorController extends Controller
{
    public function index()
    {
        return view('auth.two-factor');
    }

    public function show()
    {
        return view('auth.verify-2fa');
    }

    public function generateCode()
    {
        $user = Auth::user();
        $user->generateTwoFactorCode();

        return redirect()->route('2fa.verify.show')
            ->with('status', 'Un nouveau code a été envoyé à votre adresse email.');
    }

    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        $user = Auth::user();

        if ($user->verifyTwoFactorCode($request->code)) {
            // Marquer l'utilisateur comme vérifié pour cette session
            session(['2fa_verified' => true]);

            return redirect()->intended(route('dashboard'))
                ->with('status', 'Vérification réussie !');
        }

        return back()->withErrors([
            'code' => 'Le code fourni est invalide ou a expiré.',
        ]);
    }

    public function toggle(Request $request)
    {
        $user = $request->user();
        $user->two_factor_enabled = !$user->two_factor_enabled;
        $user->save();

        return back()->with(
            'status',
            $user->two_factor_enabled
            ? 'L\'authentification à deux facteurs a été activée.'
            : 'L\'authentification à deux facteurs a été désactivée.'
        );
    }

    public function resend()
    {
        return $this->generateCode();
    }
}
