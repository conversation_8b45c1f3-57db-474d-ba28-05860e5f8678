<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Mail\VerificationCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;

class TwoFactorController extends Controller
{
    public function index()
    {
        return view('auth.two-factor');
    }

    public function show()
    {
        return view('auth.verify-2fa');
    }

    public function generateCode()
    {
        $user = Auth::user();
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        $mail = new VerificationCodeMail();
        $mail->code = $code;
        $mail->user = $user;
        Mail::to($user->email)->send($mail);

        session(['2fa_code' => $code]);

        return redirect()->route('2fa.verify.show')
            ->with('status', 'Un nouveau code a été envoyé à votre adresse email.');
    }

    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        if ($request->code === session('2fa_code')) {
            session()->forget('2fa_code');
            return redirect()->intended(route('admin.dashboard'))
                ->with('status', 'Vérification réussie !');
        }

        return back()->withErrors([
            'code' => 'Le code fourni est invalide.',
        ]);
    }

    public function toggle(Request $request)
    {
        $user = $request->user();
        $user->two_factor_enabled = !$user->two_factor_enabled;
        $user->save();

        return back()->with(
            'status',
            $user->two_factor_enabled
            ? 'L\'authentification à deux facteurs a été activée.'
            : 'L\'authentification à deux facteurs a été désactivée.'
        );
    }

    public function resend()
    {
        return $this->generateCode();
    }
}
