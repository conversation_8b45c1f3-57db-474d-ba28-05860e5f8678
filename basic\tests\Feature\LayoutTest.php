<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LayoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_dashboard_layout_loads_correctly()
    {
        $user = User::factory()->create([
            'two_factor_enabled' => false,
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Dashboard');
        $response->assertSee('Website Traffic');
        $response->assertSee('Monthly Sales');
    }

    public function test_sidebar_contains_navigation_links()
    {
        $user = User::factory()->create([
            'two_factor_enabled' => false,
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Dashboard');
        $response->assertSee('Authentification 2FA');
    }

    public function test_logo_links_are_correct()
    {
        $user = User::factory()->create([
            'two_factor_enabled' => false,
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        // Vérifier que les liens du logo pointent vers le dashboard
        $response->assertSee('href="' . route('dashboard') . '"', false);
    }

    public function test_custom_css_is_loaded()
    {
        $user = User::factory()->create([
            'two_factor_enabled' => false,
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        // Vérifier que le CSS personnalisé est inclus
        $response->assertSee('backend/assets/css/custom.css');
    }

    public function test_2fa_page_loads_correctly()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/2fa');

        $response->assertStatus(200);
        $response->assertSee('Authentification à deux facteurs');
    }
}
