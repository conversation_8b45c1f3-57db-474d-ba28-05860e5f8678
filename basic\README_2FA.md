# Authentification à Deux Facteurs (2FA)

## Problèmes Résolus

Le système 2FA a été entièrement refondu pour corriger les problèmes suivants :

1. **Modèle User non configuré** : Le trait `TwoFactorAuthentication` a été ajouté au modèle User
2. **Implémentations incohérentes** : Unification de la logique 2FA dans le trait
3. **Processus de connexion non intégré** : La 2FA est maintenant vérifiée lors de la connexion
4. **Middleware manquant** : Ajout du middleware `TwoFactorMiddleware` pour protéger les routes

## Fonctionnement

### 1. Activation/Désactivation de la 2FA

Les utilisateurs peuvent activer/désactiver la 2FA depuis la page `/2fa` :

```php
// Dans TwoFactorController
public function toggle(Request $request)
{
    $user = $request->user();
    $user->two_factor_enabled = !$user->two_factor_enabled;
    $user->save();
}
```

### 2. Processus de Connexion avec 2FA

1. L'utilisateur se connecte avec email/mot de passe
2. Si la 2FA est activée, un code est généré et envoyé par email
3. L'utilisateur est redirigé vers la page de vérification
4. Après vérification du code, l'accès est accordé

### 3. Protection des Routes

Le middleware `2fa` protège automatiquement les routes sensibles :

```php
Route::middleware(['auth', '2fa'])->group(function () {
    Route::get('/dashboard', ...);
    Route::get('/profile', ...);
    // Autres routes protégées
});
```

## Configuration

### Variables d'Environnement

Assurez-vous que la configuration email est correcte dans `.env` :

```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_FROM_ADDRESS="<EMAIL>"
```

Pour le développement, vous pouvez utiliser :
```env
MAIL_MAILER=log
```

### Base de Données

Les colonnes suivantes ont été ajoutées à la table `users` :

- `two_factor_enabled` (boolean) : Active/désactive la 2FA
- `two_factor_code` (string) : Code temporaire de vérification
- `two_factor_expires_at` (datetime) : Date d'expiration du code

## Utilisation

### Comptes de Test

Deux comptes de test ont été créés :

1. **Avec 2FA activée** :
   - Email : `<EMAIL>`
   - Mot de passe : `password`

2. **Sans 2FA** :
   - Email : `<EMAIL>`
   - Mot de passe : `password`

### Test Manuel

1. Connectez-vous avec `<EMAIL>` / `password`
2. Vous serez redirigé vers `/2fa/verify`
3. Vérifiez les logs Laravel pour voir le code : `tail -f storage/logs/laravel.log`
4. Entrez le code pour accéder au dashboard

### Commandes Utiles

```bash
# Créer les comptes de test
php artisan db:seed --class=TestUserSeeder

# Exécuter les tests 2FA
php artisan test --filter=TwoFactorAuthTest

# Vider les caches
php artisan route:clear
php artisan config:clear
```

## Sécurité

- Les codes 2FA expirent après 10 minutes
- Les codes sont supprimés après utilisation
- La session 2FA est nettoyée lors de la déconnexion
- Les codes sont générés de manière sécurisée (6 chiffres aléatoires)

## Dépannage

### Problème : "Le code fourni est invalide"

1. Vérifiez que le code n'a pas expiré (10 minutes)
2. Assurez-vous d'utiliser le code le plus récent
3. Vérifiez les logs pour voir le code généré

### Problème : "Aucun email reçu"

1. Vérifiez la configuration MAIL dans `.env`
2. Pour le développement, vérifiez `storage/logs/laravel.log`
3. Testez l'envoi d'email avec `php artisan tinker`

### Problème : "Redirection infinie"

1. Vérifiez que le middleware `2fa` n'est pas appliqué aux routes 2FA
2. Assurez-vous que `session('2fa_verified')` est définie après vérification

## API du Trait TwoFactorAuthentication

```php
// Générer et envoyer un code 2FA
$user->generateTwoFactorCode();

// Vérifier un code
$isValid = $user->verifyTwoFactorCode($code);

// Réinitialiser le code
$user->resetTwoFactorCode();

// Activer/désactiver la 2FA
$user->toggleTwoFactor();
```
