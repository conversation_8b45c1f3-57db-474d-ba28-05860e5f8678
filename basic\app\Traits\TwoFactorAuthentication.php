<?php

namespace App\Traits;

use App\Mail\VerificationCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

trait TwoFactorAuthentication
{
    /**
     * Génère et envoie un nouveau code 2FA
     */
    public function generateTwoFactorCode()
    {
        $this->two_factor_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        $this->two_factor_expires_at = now()->addMinutes(10);
        $this->save();

        Mail::to($this->email)->send(new VerificationCodeMail($this->two_factor_code, $this));
    }

    /**
     * Vérifie si le code 2FA est valide
     */
    public function verifyTwoFactorCode($code)
    {
        if (
            $this->two_factor_code === $code &&
            $this->two_factor_expires_at > now()
        ) {
            $this->resetTwoFactorCode();
            return true;
        }

        return false;
    }

    /**
     * Réinitialise le code 2FA
     */
    public function resetTwoFactorCode()
    {
        $this->update([
            'two_factor_code' => null,
            'two_factor_expires_at' => null,
        ]);
    }

    /**
     * Active ou désactive l'authentification à deux facteurs
     */
    public function toggleTwoFactor()
    {
        $this->two_factor_enabled = !$this->two_factor_enabled;
        $this->save();
    }
}
