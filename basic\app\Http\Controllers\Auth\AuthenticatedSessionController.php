<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        $user = $request->user();

        // Si la 2FA est activée pour cet utilisateur
        if ($user && $user->two_factor_enabled) {
            // Générer et envoyer le code 2FA
            $user->generateTwoFactorCode();

            return redirect()->route('2fa.verify.show')
                ->with('status', 'Un code de vérification a été envoyé à votre adresse email.');
        }

        // Si pas de 2FA, redirection normale
        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // Nettoyer la session 2FA
        $request->session()->forget('2fa_verified');

        return redirect('/');
    }
}
