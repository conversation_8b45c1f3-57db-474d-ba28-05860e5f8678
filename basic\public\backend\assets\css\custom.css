/* Corrections pour l'affichage du dashboard */

/* Assurer que le logo est bien visible */
.logo-box {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 1rem;
}

.logo-box .logo {
    display: inline-block;
}

.logo-box .logo img {
    max-height: 40px;
    width: auto;
}

/* Corriger l'espacement du contenu principal */
.content-page {
    margin-left: 260px; /* Largeur du sidebar */
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
    .content-page {
        margin-left: 0;
    }
}

/* Assurer que le sidebar a la bonne largeur */
.app-sidebar-menu {
    width: 260px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    background: #fff;
    border-right: 1px solid #e9ecef;
    z-index: 1000;
}

/* Ajuster la topbar pour qu'elle ne chevauche pas le sidebar */
.topbar-custom {
    margin-left: 260px;
    transition: margin-left 0.3s ease;
}

@media (max-width: 768px) {
    .topbar-custom {
        margin-left: 0;
    }
}

/* Améliorer l'apparence du menu */
#side-menu {
    padding: 0;
    margin: 0;
}

#side-menu li {
    list-style: none;
}

#side-menu .menu-title {
    padding: 0.75rem 1.5rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

#side-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
}

#side-menu a:hover {
    background-color: #f8f9fa;
    color: #007bff;
}

#side-menu a.active {
    background-color: #007bff;
    color: #fff;
}

/* Icônes du menu */
#side-menu i {
    margin-right: 0.5rem;
    width: 16px;
    height: 16px;
}

/* Espacement du contenu */
.container-xxl {
    padding: 1.5rem;
}

/* Assurer que les cartes ont un bon espacement */
.card {
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Corriger l'affichage des graphiques */
.apex-charts {
    min-height: 50px;
}

/* Style pour les statistiques */
.fs-22 {
    font-size: 1.375rem !important;
}

.fs-18 {
    font-size: 1.125rem !important;
}

.fs-14 {
    font-size: 0.875rem !important;
}
